"""
Enhanced Azure Exam Analyzer - Ultra Accuracy Edition
Significantly improved accuracy through better prompting, validation, and error detection
"""

import subprocess
import time
import tkinter as tk
from tkinter import ttk
from pynput import keyboard
from pynput.keyboard import Key
import threading
import os
import base64
import requests
import json
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
from datetime import datetime
from openai import OpenAI
from collections import Counter
import re
from typing import Dict, List, Tuple, Optional
import asyncio


class UltraAccuracyAnalyzer:
    """Ultra-accurate analyzer with advanced reasoning and validation."""
    
    def __init__(self, client, models):
        self.client = client
        self.models = models
        
    def create_ultra_precise_prompt(self):
        """Create an ultra-precise prompt that forces careful analysis."""
        return """You are an Azure certification expert with a 100% accuracy requirement. 
        
**CRITICAL ACCURACY RULES:**
1. Read EVERY word in the question - missing even one word can lead to wrong answers
2. Azure exam questions often contain TRAPS - options that seem right but miss a key requirement
3. The MOST COMPLETE solution is usually correct - not just the simplest
4. Pay extreme attention to words like: "MOST", "LEAST", "MINIMUM", "<PERSON><PERSON>IMUM", "ONLY", "ALL", "BEST"
5. Consider Azure BEST PRACTICES and MICROSOFT RECOMMENDATIONS

**SYSTEMATIC ANALYSIS PROCESS:**

**STEP 1: EXTRACT EXACT REQUIREMENTS**
List EVERY requirement mentioned:
- Functional requirements (what must work)
- Non-functional requirements (performance, security, cost)
- Constraints (what cannot be used)
- Implicit requirements (best practices implied)

**STEP 2: IDENTIFY THE TRAP**
Azure exams ALWAYS have trap answers. Identify:
- Which options look correct but miss something
- What subtle requirement eliminates certain options
- Which option is the "obvious wrong" choice

**STEP 3: DETAILED OPTION ANALYSIS**
For EACH option, answer:
- Does it meet ALL functional requirements? (YES/NO + why)
- Does it meet ALL non-functional requirements? (YES/NO + why)
- Does it follow Azure best practices? (YES/NO + why)
- What's the FATAL FLAW that eliminates this option? (if any)
- Confidence this is correct: (0-100%)

**STEP 4: ELIMINATION REASONING**
- Which options can be immediately eliminated and why?
- Between remaining options, what distinguishes them?
- Which option is MOST COMPLETE for the scenario?

**STEP 5: FINAL VALIDATION**
Before choosing, verify:
- Have I considered ALL requirements?
- Does my answer address the EXACT question asked?
- Am I falling for a common trap?
- Would an Azure architect actually implement this?

**OUTPUT FORMAT (MUST FOLLOW EXACTLY):**
REQUIREMENTS_EXTRACTED:
- Requirement 1: [exact text from question]
- Requirement 2: [exact text from question]
[continue for all requirements]

TRAP_IDENTIFIED: [what trap exists in this question]

OPTION_A_ANALYSIS:
- Meets functional requirements: [YES/NO - specific reason]
- Meets non-functional requirements: [YES/NO - specific reason]
- Follows best practices: [YES/NO - specific reason]
- Fatal flaw: [if any, or "None"]
- Confidence: [0-100]%

OPTION_B_ANALYSIS:
[same structure]

OPTION_C_ANALYSIS:
[same structure]

OPTION_D_ANALYSIS:
[same structure]

ELIMINATED_OPTIONS: [List options that definitely don't work and why]

FINAL_ANSWER: [A/B/C/D]
CERTAINTY_LEVEL: [0-100]%
KEY_REASONING: [One sentence explaining why this is definitively correct]
"""

    def create_cross_check_prompt(self, question_text: str = None):
        """Create a cross-checking prompt to verify answers."""
        return """You are a second Azure expert reviewing an answer. Your job is to FIND ERRORS.

**YOUR TASK:**
1. Look for any missed requirements
2. Check if the answer violates Azure best practices
3. Verify the technical accuracy
4. Identify if a better option exists

**BE EXTREMELY CRITICAL** - Assume the first analysis might be wrong.

**CHECK THESE COMMON MISTAKES:**
- Missing cost optimization requirements
- Ignoring security/compliance requirements
- Not considering scalability needs
- Choosing overly complex solutions when simple ones work
- Missing the word "minimum" or "least" in requirements
- Not noticing geographic/region constraints
- Ignoring high availability requirements

**VERIFICATION OUTPUT:**
MISSED_REQUIREMENTS: [any requirements not properly considered]
TECHNICAL_ERRORS: [any technical inaccuracies]
BETTER_OPTION_EXISTS: [YES/NO - if yes, which one and why]
CONFIDENCE_IN_ORIGINAL: [0-100]%
VERIFIED_ANSWER: [A/B/C/D]
CHANGE_REASON: [if different from original, explain why]
"""

    def create_azure_expert_prompt(self):
        """Specialized prompt for Azure-specific knowledge."""
        return """You are an Azure Solutions Architect with deep expertise in ALL Azure services.

**AZURE-SPECIFIC ANALYSIS REQUIREMENTS:**

1. **SERVICE SELECTION RULES:**
   - Always prefer PaaS over IaaS when possible
   - Use managed services over self-managed
   - Consider Azure-native solutions first
   - Factor in regional availability

2. **COST OPTIMIZATION CHECKS:**
   - Is this the most cost-effective solution?
   - Are there reserved instances or spot instances applicable?
   - Is auto-scaling configured appropriately?

3. **SECURITY & COMPLIANCE:**
   - Does it follow Zero Trust principles?
   - Are managed identities used where possible?
   - Is data encrypted at rest and in transit?
   - Does it meet compliance requirements mentioned?

4. **HIGH AVAILABILITY & DISASTER RECOVERY:**
   - What's the SLA of this solution?
   - Is it resilient to region failures if required?
   - Are availability zones utilized?

5. **PERFORMANCE & SCALABILITY:**
   - Can it handle the stated load?
   - Is it using appropriate SKUs/tiers?
   - Are there performance bottlenecks?

**ANALYZE EACH OPTION AGAINST THESE AZURE PRINCIPLES:**
- Well-Architected Framework pillars (Reliability, Security, Cost, Operations, Performance)
- Azure best practices for the specific services mentioned
- Common Azure exam patterns and expected answers

**AZURE EXPERT OUTPUT:**
SERVICE_EVALUATION:
- Option A: [Azure services used and appropriateness]
- Option B: [Azure services used and appropriateness]
- Option C: [Azure services used and appropriateness]
- Option D: [Azure services used and appropriateness]

BEST_PRACTICES_SCORE:
- Option A: [0-10 score with reason]
- Option B: [0-10 score with reason]
- Option C: [0-10 score with reason]
- Option D: [0-10 score with reason]

AZURE_RECOMMENDED_ANSWER: [A/B/C/D]
AZURE_CONFIDENCE: [0-100]%
AZURE_RATIONALE: [Why this follows Azure best practices]
"""

    async def enhanced_multi_phase_analysis(self, image_content: List[Dict]) -> Dict:
        """Multi-phase analysis with enhanced accuracy checks."""
        try:
            print("🎯 Starting Ultra-Accuracy Analysis Pipeline...")
            
            # Phase 1: Initial Deep Analysis
            print("📊 Phase 1: Deep Requirement Analysis...")
            initial_analyses = await self._phase1_deep_analysis(image_content)
            
            # Phase 2: Azure Expert Review
            print("☁️ Phase 2: Azure Expert Validation...")
            azure_validated = await self._phase2_azure_expert_review(initial_analyses, image_content)
            
            # Phase 3: Cross-Validation
            print("🔍 Phase 3: Cross-Validation & Error Detection...")
            cross_validated = await self._phase3_cross_validation(azure_validated, image_content)
            
            # Phase 4: Consensus with Conflict Resolution
            print("🤝 Phase 4: Building High-Confidence Consensus...")
            consensus = await self._phase4_intelligent_consensus(cross_validated, image_content)
            
            # Phase 5: Final Sanity Check
            print("✅ Phase 5: Final Sanity Verification...")
            final_result = await self._phase5_sanity_check(consensus, image_content)
            
            return final_result
            
        except Exception as e:
            print(f"❌ Ultra-accuracy pipeline failed: {e}")
            return {"error": str(e), "final_answer": "ERROR", "confidence": 0}

    async def _phase1_deep_analysis(self, image_content: List[Dict]) -> List[Dict]:
        """Phase 1: Deep requirement extraction and analysis."""
        analyses = []
        
        for model in self.models:
            try:
                print(f"🧠 {model}: Performing deep requirement analysis...")
                
                message_content = [
                    {"type": "text", "text": self.create_ultra_precise_prompt()}
                ] + image_content
                
                completion = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": message_content}],
                    max_tokens=4096,
                    temperature=0.0,  # Zero temperature for consistency
                )
                
                if completion.choices and completion.choices[0].message:
                    analysis_text = completion.choices[0].message.content.strip()
                    parsed = self._parse_ultra_analysis(analysis_text)
                    parsed["model"] = model
                    parsed["raw_text"] = analysis_text
                    analyses.append(parsed)
                    
                    answer = parsed.get('final_answer', 'N/A')
                    certainty = parsed.get('certainty_level', 0)
                    print(f"✅ {model}: Answer {answer} with {certainty}% certainty")
                    
            except Exception as e:
                print(f"❌ {model} analysis failed: {e}")
                
        return analyses

    async def _phase2_azure_expert_review(self, analyses: List[Dict], image_content: List[Dict]) -> List[Dict]:
        """Phase 2: Azure-specific expert validation."""
        azure_reviewed = []
        
        # Use the most reliable model for Azure expertise
        azure_expert_model = "anthropic/claude-3.5-sonnet"  # Claude tends to be best for Azure
        
        for analysis in analyses:
            try:
                print(f"☁️ Azure expert reviewing {analysis['model']}'s analysis...")
                
                message_content = [
                    {"type": "text", "text": self.create_azure_expert_prompt()}
                ] + image_content
                
                completion = self.client.chat.completions.create(
                    model=azure_expert_model,
                    messages=[{"role": "user", "content": message_content}],
                    max_tokens=3072,
                    temperature=0.0,
                )
                
                if completion.choices and completion.choices[0].message:
                    review_text = completion.choices[0].message.content.strip()
                    analysis['azure_review'] = review_text
                    
                    # Extract Azure recommendation
                    azure_answer = self._extract_pattern(
                        review_text, 
                        r'AZURE_RECOMMENDED_ANSWER:\s*([A-D])'
                    )
                    azure_confidence = self._extract_pattern(
                        review_text,
                        r'AZURE_CONFIDENCE:\s*(\d+)',
                        return_type=int
                    )
                    
                    analysis['azure_answer'] = azure_answer
                    analysis['azure_confidence'] = azure_confidence or 0
                    
                    azure_reviewed.append(analysis)
                    print(f"✅ Azure expert suggests: {azure_answer} ({azure_confidence}% confidence)")
                    
            except Exception as e:
                print(f"❌ Azure review failed: {e}")
                azure_reviewed.append(analysis)
                
        return azure_reviewed

    async def _phase3_cross_validation(self, analyses: List[Dict], image_content: List[Dict]) -> List[Dict]:
        """Phase 3: Cross-validation to catch errors."""
        cross_validated = []
        
        for analysis in analyses:
            try:
                model = analysis['model']
                print(f"🔍 Cross-validating {model}'s answer...")
                
                # Create cross-check prompt with the original analysis
                cross_check_prompt = self.create_cross_check_prompt() + \
                    f"\n\nORIGINAL ANALYSIS TO VERIFY:\n{analysis.get('raw_text', '')}"
                
                message_content = [
                    {"type": "text", "text": cross_check_prompt}
                ] + image_content
                
                # Use a different model for cross-validation
                validator_model = self.models[0] if model != self.models[0] else self.models[1]
                
                completion = self.client.chat.completions.create(
                    model=validator_model,
                    messages=[{"role": "user", "content": message_content}],
                    max_tokens=2048,
                    temperature=0.0,
                )
                
                if completion.choices and completion.choices[0].message:
                    validation_text = completion.choices[0].message.content.strip()
                    
                    verified_answer = self._extract_pattern(
                        validation_text,
                        r'VERIFIED_ANSWER:\s*([A-D])'
                    )
                    confidence_in_original = self._extract_pattern(
                        validation_text,
                        r'CONFIDENCE_IN_ORIGINAL:\s*(\d+)',
                        return_type=int
                    )
                    
                    analysis['cross_validated_answer'] = verified_answer
                    analysis['cross_validation_confidence'] = confidence_in_original or 0
                    analysis['validation_text'] = validation_text
                    
                    if verified_answer != analysis.get('final_answer'):
                        print(f"⚠️ Cross-validation suggests different answer: {verified_answer}")
                    else:
                        print(f"✅ Cross-validation confirms: {verified_answer}")
                        
                cross_validated.append(analysis)
                
            except Exception as e:
                print(f"❌ Cross-validation failed: {e}")
                cross_validated.append(analysis)
                
        return cross_validated

    async def _phase4_intelligent_consensus(self, analyses: List[Dict], image_content: List[Dict]) -> Dict:
        """Phase 4: Build intelligent consensus from all analyses."""
        if not analyses:
            return {"error": "No analyses available", "final_answer": "ERROR", "confidence": 0}
        
        print("🤝 Building intelligent consensus...")
        
        # Collect all answers with their confidence scores
        answer_votes = {}
        
        for analysis in analyses:
            # Original answer
            orig_answer = analysis.get('final_answer')
            orig_confidence = analysis.get('certainty_level', 50)
            
            if orig_answer:
                if orig_answer not in answer_votes:
                    answer_votes[orig_answer] = []
                answer_votes[orig_answer].append({
                    'source': 'original',
                    'model': analysis['model'],
                    'confidence': orig_confidence
                })
            
            # Azure expert answer
            azure_answer = analysis.get('azure_answer')
            azure_confidence = analysis.get('azure_confidence', 0)
            
            if azure_answer:
                if azure_answer not in answer_votes:
                    answer_votes[azure_answer] = []
                answer_votes[azure_answer].append({
                    'source': 'azure_expert',
                    'model': 'azure_validation',
                    'confidence': azure_confidence
                })
            
            # Cross-validated answer
            cross_answer = analysis.get('cross_validated_answer')
            cross_confidence = analysis.get('cross_validation_confidence', 0)
            
            if cross_answer:
                if cross_answer not in answer_votes:
                    answer_votes[cross_answer] = []
                answer_votes[cross_answer].append({
                    'source': 'cross_validation',
                    'model': 'cross_validator',
                    'confidence': cross_confidence
                })
        
        # Calculate weighted scores for each answer
        answer_scores = {}
        for answer, votes in answer_votes.items():
            # Weight by confidence and source reliability
            total_score = 0
            for vote in votes:
                weight = 1.0
                if vote['source'] == 'azure_expert':
                    weight = 1.5  # Azure expert opinions weighted higher
                elif vote['source'] == 'cross_validation':
                    weight = 1.2  # Cross-validation also important
                    
                total_score += vote['confidence'] * weight
                
            answer_scores[answer] = total_score / len(votes)
        
        # Find the best answer
        if answer_scores:
            best_answer = max(answer_scores.keys(), key=lambda k: answer_scores[k])
            best_score = answer_scores[best_answer]
            
            # Calculate final confidence
            final_confidence = min(100, int(best_score))
            
            # Check for strong disagreement
            scores_list = list(answer_scores.values())
            if len(scores_list) > 1:
                second_best = sorted(scores_list)[-2]
                if best_score - second_best < 20:  # Close call
                    final_confidence = min(final_confidence, 70)
                    print(f"⚠️ Close decision between options (difference: {best_score - second_best:.1f})")
            
            print(f"📊 Answer distribution: {dict(answer_votes.keys())}")
            print(f"📊 Weighted scores: {answer_scores}")
            print(f"🎯 Consensus answer: {best_answer} (confidence: {final_confidence}%)")
            
            return {
                'final_answer': best_answer,
                'confidence': final_confidence,
                'answer_distribution': answer_votes,
                'weighted_scores': answer_scores,
                'consensus_method': 'intelligent_weighted'
            }
        
        # Fallback
        return {
            'final_answer': analyses[0].get('final_answer', 'ERROR'),
            'confidence': 50,
            'consensus_method': 'fallback'
        }

    async def _phase5_sanity_check(self, consensus: Dict, image_content: List[Dict]) -> Dict:
        """Phase 5: Final sanity check to catch obvious errors."""
        try:
            print("✅ Performing final sanity check...")
            
            sanity_prompt = """You are performing a FINAL SANITY CHECK on an Azure exam answer.

**QUICK CHECKS:**
1. Does the answer make logical sense for Azure?
2. Is it choosing an overly complex solution when a simple one exists?
3. Does it violate any Azure fundamental principles?
4. Would this solution actually work in production?

**THE PROPOSED ANSWER:** """ + consensus.get('final_answer', 'ERROR') + """

**SANITY CHECK RESULT:**
MAKES_SENSE: [YES/NO - brief reason]
COMMON_TRAP: [Is this falling for a common exam trap?]
PRODUCTION_READY: [Would this work in real Azure?]
FINAL_VERDICT: [APPROVE/REJECT]
CONFIRMED_ANSWER: [A/B/C/D]
SANITY_CONFIDENCE: [0-100]%
"""
            
            message_content = [
                {"type": "text", "text": sanity_prompt}
            ] + image_content
            
            completion = self.client.chat.completions.create(
                model=self.models[-1],  # Use most reliable model
                messages=[{"role": "user", "content": message_content}],
                max_tokens=1024,
                temperature=0.0,
            )
            
            if completion.choices and completion.choices[0].message:
                sanity_text = completion.choices[0].message.content.strip()
                
                verdict = self._extract_pattern(sanity_text, r'FINAL_VERDICT:\s*(\w+)')
                confirmed_answer = self._extract_pattern(sanity_text, r'CONFIRMED_ANSWER:\s*([A-D])')
                sanity_confidence = self._extract_pattern(
                    sanity_text, 
                    r'SANITY_CONFIDENCE:\s*(\d+)',
                    return_type=int
                )
                
                if verdict == "REJECT" and confirmed_answer:
                    print(f"⚠️ Sanity check suggests different answer: {confirmed_answer}")
                    consensus['final_answer'] = confirmed_answer
                    consensus['confidence'] = min(consensus['confidence'], sanity_confidence or 50)
                    consensus['sanity_override'] = True
                else:
                    print(f"✅ Sanity check confirms answer: {consensus['final_answer']}")
                    
                consensus['sanity_check_performed'] = True
                consensus['sanity_confidence'] = sanity_confidence
                
        except Exception as e:
            print(f"❌ Sanity check failed: {e}")
            
        return consensus

    def _parse_ultra_analysis(self, text: str) -> Dict:
        """Parse ultra-precise analysis format."""
        parsed = {}
        
        # Extract final answer and certainty
        final_answer = self._extract_pattern(text, r'FINAL_ANSWER:\s*([A-D])')
        certainty = self._extract_pattern(text, r'CERTAINTY_LEVEL:\s*(\d+)', return_type=int)
        key_reasoning = self._extract_pattern(text, r'KEY_REASONING:\s*(.+?)(?=\n|$)')
        
        parsed['final_answer'] = final_answer
        parsed['certainty_level'] = certainty or 0
        parsed['key_reasoning'] = key_reasoning
        
        # Extract trap identification
        trap = self._extract_pattern(text, r'TRAP_IDENTIFIED:\s*(.+?)(?=\n[A-Z_]+:|$)')
        parsed['trap_identified'] = trap
        
        return parsed

    def _extract_pattern(self, text: str, pattern: str, return_type=str):
        """Extract pattern from text with type conversion."""
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if match:
            value = match.group(1).strip()
            if return_type == int:
                try:
                    return int(value)
                except:
                    return None
            return value
        return None


class EnhancedAzureExamAnalyzer:
    """Enhanced Azure Exam Analyzer with Ultra Accuracy."""

    def __init__(self):
        self.hotkey_detected = False
        self.send_screenshots_detected = False
        self.running = True
        self.listener = None
        self.root = None
        
        # Your API key
        self.api_key = "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"

        # Updated model configuration for better accuracy
        self.models = [
            "anthropic/claude-3.5-sonnet",  # Best for Azure knowledge
            "openai/gpt-4-turbo",  # Good for reasoning
            "google/gemini-1.5-pro"  # Good for visual understanding
        ]

        # Initialize OpenAI client for OpenRouter
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        )

        # Initialize ultra accuracy analyzer
        self.ultra_analyzer = UltraAccuracyAnalyzer(self.client, self.models)

        # Screenshot collection
        self.collected_screenshots = []
        self.screenshot_lock = threading.Lock()

        # Temp directory for screenshots
        self.temp_dir = Path(tempfile.gettempdir()) / "azure_exam_analyzer"
        self.temp_dir.mkdir(exist_ok=True)

        # ShareX directory monitoring
        self.sharex_dir = Path("C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX/Screenshots/2025-08")
        if not self.sharex_dir.exists():
            self.sharex_dir.mkdir(parents=True, exist_ok=True)

        # Performance tracking
        self.answer_history = []
        self.accuracy_mode = "ULTRA"  # ULTRA, HIGH, FAST

        # Create persistent root window
        self.setup_root()
        self.setup_file_monitoring()

    async def analyze_with_ultra_accuracy(self, image_paths: List[str]) -> str:
        """Main analysis method with ultra accuracy."""
        try:
            print(f"🚀 Starting ULTRA ACCURACY analysis of {len(image_paths)} screenshot(s)...")
            print(f"🤖 Models: {', '.join(self.models)}")
            print("=" * 60)

            # Prepare image content
            image_content = []
            for image_path in image_paths:
                with open(image_path, "rb") as image_file:
                    base64_image = base64.b64encode(image_file.read()).decode('utf-8')
                    image_content.append({
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{base64_image}"}
                    })

            # Use ultra accuracy pipeline
            result = await self.ultra_analyzer.enhanced_multi_phase_analysis(image_content)

            # Display results
            self._display_ultra_results(result)

            return result.get('final_answer', 'ERROR')

        except Exception as e:
            error_msg = f"❌ Ultra accuracy analysis failed: {str(e)}"
            print(f"🚨 {error_msg}")
            self.show_answer_bubble(error_msg)
            return error_msg

    def _display_ultra_results(self, result: Dict):
        """Display ultra accuracy results."""
        final_answer = result.get('final_answer', 'ERROR')
        confidence = result.get('confidence', 0)

        # Determine confidence level message
        if confidence >= 90:
            confidence_msg = "VERY HIGH"
            symbol = "🟢"
        elif confidence >= 75:
            confidence_msg = "HIGH"
            symbol = "🟡"
        elif confidence >= 60:
            confidence_msg = "MODERATE"
            symbol = "🟠"
        else:
            confidence_msg = "LOW - VERIFY"
            symbol = "🔴"

        # Create display text
        display_text = f"{symbol} Answer: {final_answer}\nConfidence: {confidence}% ({confidence_msg})"

        if result.get('sanity_override'):
            display_text += "\n⚠️ Sanity check override applied"

        # Show the answer
        self.show_answer_bubble(display_text)

        # Console output
        print("=" * 60)
        print(f"🎯 FINAL ANSWER: {final_answer}")
        print(f"📊 CONFIDENCE: {confidence}% ({confidence_msg})")
        
        if 'answer_distribution' in result:
            print(f"📈 Answer votes: {list(result['answer_distribution'].keys())}")
        
        if 'weighted_scores' in result:
            print(f"⚖️ Weighted scores: {result['weighted_scores']}")
            
        print("=" * 60)

    def show_answer_bubble(self, answer):
        """Show answer bubble with enhanced styling."""
        def create_bubble():
            bubble = tk.Toplevel(self.root)
            bubble.title("Azure Exam Answer - Ultra Accuracy")
            bubble.overrideredirect(True)
            
            # Determine background color based on confidence
            if "VERY HIGH" in answer:
                bg_color = '#2d5a2d'  # Green
            elif "HIGH" in answer:
                bg_color = '#4a4a2d'  # Yellow-ish
            elif "MODERATE" in answer:
                bg_color = '#5a3d2d'  # Orange-ish
            else:
                bg_color = '#5a2d2d'  # Red-ish
                
            bubble.configure(bg=bg_color)

            label = tk.Label(bubble, text=answer, font=('Segoe UI', 12, 'bold'),
                           bg=bg_color, fg='#ffffff', wraplength=450, justify='left',
                           padx=20, pady=15)
            label.pack()

            # Position at top-left
            bubble.geometry("+50+50")
            bubble.lift()
            bubble.attributes('-topmost', True)

            # Auto-close after 30 seconds
            bubble.after(30000, bubble.destroy)

        if self.root:
            self.root.after(0, create_bubble)

    def process_collected_screenshots(self):
        """Process all collected screenshots with ultra accuracy."""
        with self.screenshot_lock:
            print(f"🔍 Checking for screenshots...")

            # Try manual scan if no screenshots collected
            if not self.collected_screenshots:
                print("🔍 Scanning ShareX directory for recent files...")
                recent_files = self.scan_for_recent_screenshots()

                if recent_files:
                    print(f"📸 Found {len(recent_files)} recent screenshot(s)")
                    for file_path in recent_files[:5]:  # Limit to 5 most recent
                        try:
                            temp_path = tempfile.mktemp(suffix='.png')
                            shutil.copy2(file_path, temp_path)
                            self.collected_screenshots.append(temp_path)
                            print(f"📸 Collected: {file_path.name}")
                        except Exception as e:
                            print(f"❌ Error collecting {file_path.name}: {e}")
                else:
                    print("❌ No recent screenshots found")
                    return

            if not self.collected_screenshots:
                print("❌ No screenshots available for analysis")
                return

            try:
                print(f"🔄 Processing {len(self.collected_screenshots)} screenshot(s)...")
                print(f"⚡ Accuracy Mode: {self.accuracy_mode}")
                print("=" * 60)

                # Use async analysis
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    result = loop.run_until_complete(
                        self.analyze_with_ultra_accuracy(self.collected_screenshots)
                    )
                finally:
                    loop.close()

                # Clean up
                print("🧹 Cleaning up temporary files...")
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                    except:
                        pass

                self.collected_screenshots.clear()
                print("✅ Ready for next question (Ctrl+D to start)")

            except Exception as e:
                print(f"❌ Error processing screenshots: {e}")
                # Clean up on error
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(